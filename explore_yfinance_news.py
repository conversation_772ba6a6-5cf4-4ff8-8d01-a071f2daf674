#!/usr/bin/env python3
"""
Script to explore what yfinance.news() provides for different tickers
This helps understand how articles are associated with specific stocks
"""

import yfinance as yf
import json
import pprint
from datetime import datetime
import argparse


def explore_ticker_news(ticker, max_articles=5):
    """
    Explore news data structure for a specific ticker
    """
    print(f"\n{'='*60}")
    print(f"EXPLORING NEWS FOR TICKER: {ticker}")
    print(f"{'='*60}")
    
    try:
        # Create yfinance Ticker object
        stock = yf.Ticker(ticker)
        
        # Get news data
        news = stock.news
        
        if not news:
            print(f"❌ No news found for {ticker}")
            return
        
        print(f"✅ Found {len(news)} news articles for {ticker}")
        print(f"📰 Showing first {min(max_articles, len(news))} articles:\n")
        
        for i, article in enumerate(news[:max_articles]):
            print(f"--- ARTICLE {i+1} ---")
            
            # Handle nested content structure
            content = article.get('content', article)
            
            # Extract key fields
            title = content.get('title', 'No title')
            summary = content.get('summary', '') or content.get('description', '')
            pub_date = content.get('pubDate', '')
            provider = content.get('provider', {})
            
            print(f"Title: {title}")
            print(f"Summary: {summary[:200]}..." if len(summary) > 200 else f"Summary: {summary}")
            print(f"Published: {pub_date}")
            
            if isinstance(provider, dict):
                print(f"Provider: {provider.get('displayName', 'Unknown')}")
            else:
                print(f"Provider: {provider}")
            
            # Show URL structure
            canonical_url = content.get('canonicalUrl', {})
            click_through_url = content.get('clickThroughUrl', {})
            
            if isinstance(canonical_url, dict):
                print(f"Canonical URL: {canonical_url.get('url', 'N/A')}")
            if isinstance(click_through_url, dict):
                print(f"Click-through URL: {click_through_url.get('url', 'N/A')}")
            
            print()
        
        # Show full structure of first article for detailed analysis
        if news:
            print(f"\n{'='*40}")
            print("DETAILED STRUCTURE OF FIRST ARTICLE:")
            print(f"{'='*40}")
            pprint.pprint(news[0], width=80, depth=3)
        
    except Exception as e:
        print(f"❌ Error fetching news for {ticker}: {e}")


def compare_ticker_news(tickers):
    """
    Compare news across multiple tickers to see overlap
    """
    print(f"\n{'='*60}")
    print("COMPARING NEWS ACROSS TICKERS")
    print(f"{'='*60}")
    
    ticker_headlines = {}
    all_headlines = set()
    
    for ticker in tickers:
        try:
            stock = yf.Ticker(ticker)
            news = stock.news
            
            headlines = []
            for article in news[:5]:  # Top 5 articles
                content = article.get('content', article)
                title = content.get('title', '').strip()
                if title:
                    headlines.append(title)
                    all_headlines.add(title.lower())
            
            ticker_headlines[ticker] = headlines
            print(f"✅ {ticker}: {len(headlines)} headlines")
            
        except Exception as e:
            print(f"❌ Error with {ticker}: {e}")
            ticker_headlines[ticker] = []
    
    # Find overlapping headlines
    print(f"\n📊 HEADLINE OVERLAP ANALYSIS:")
    print(f"Total unique headlines: {len(all_headlines)}")
    
    # Check for exact matches
    overlaps = {}
    for ticker1 in ticker_headlines:
        for ticker2 in ticker_headlines:
            if ticker1 != ticker2:
                headlines1 = set(h.lower() for h in ticker_headlines[ticker1])
                headlines2 = set(h.lower() for h in ticker_headlines[ticker2])
                overlap = headlines1.intersection(headlines2)
                if overlap:
                    key = f"{ticker1}-{ticker2}"
                    overlaps[key] = list(overlap)
    
    if overlaps:
        print("\n🔄 OVERLAPPING HEADLINES FOUND:")
        for pair, shared_headlines in overlaps.items():
            print(f"{pair}: {len(shared_headlines)} shared headlines")
            for headline in shared_headlines[:3]:  # Show first 3
                print(f"  - {headline}")
    else:
        print("\n✅ No overlapping headlines found between tickers")


def analyze_news_attribution(ticker):
    """
    Analyze how news is attributed to a specific ticker
    """
    print(f"\n{'='*60}")
    print(f"NEWS ATTRIBUTION ANALYSIS FOR {ticker}")
    print(f"{'='*60}")

    try:
        stock = yf.Ticker(ticker)
        news = stock.news

        if not news:
            print(f"No news for {ticker}")
            return

        print(f"Analyzing {len(news)} articles for {ticker}...")

        # Check if ticker is mentioned in headlines/summaries
        ticker_mentioned = 0
        company_name_mentioned = 0
        tesla_mentioned = 0
        apple_mentioned = 0

        # Get company name
        try:
            info = stock.info
            company_name = info.get('longName', '').lower()
            short_name = info.get('shortName', '').lower()
        except:
            company_name = ''
            short_name = ''

        print(f"Company names to look for: '{company_name}', '{short_name}'")

        for i, article in enumerate(news[:10]):  # Check first 10
            content = article.get('content', article)
            title = content.get('title', '').lower()
            summary = content.get('summary', '').lower() or content.get('description', '').lower()

            text_to_check = f"{title} {summary}"

            ticker_in_text = ticker.lower() in text_to_check
            company_in_text = (company_name and company_name in text_to_check) or (short_name and short_name in text_to_check)
            tesla_in_text = 'tesla' in text_to_check
            apple_in_text = 'apple' in text_to_check

            if ticker_in_text:
                ticker_mentioned += 1
            if company_in_text:
                company_name_mentioned += 1
            if tesla_in_text:
                tesla_mentioned += 1
            if apple_in_text:
                apple_mentioned += 1

            if not ticker_in_text and not company_in_text and not tesla_in_text and not apple_in_text:
                print(f"⚠️  Article {i+1} doesn't mention {ticker}, company name, Tesla, or Apple:")
                print(f"   Title: {content.get('title', '')[:100]}...")

        print(f"\n📈 ATTRIBUTION RESULTS:")
        print(f"Articles mentioning ticker '{ticker}': {ticker_mentioned}/{len(news[:10])}")
        print(f"Articles mentioning company name: {company_name_mentioned}/{len(news[:10])}")
        print(f"Articles mentioning 'Tesla': {tesla_mentioned}/{len(news[:10])}")
        print(f"Articles mentioning 'Apple': {apple_mentioned}/{len(news[:10])}")

    except Exception as e:
        print(f"❌ Error analyzing attribution for {ticker}: {e}")


def deep_dive_attribution():
    """
    Deep dive into how yfinance determines article relevance
    """
    print(f"\n{'='*60}")
    print("DEEP DIVE: HOW YFINANCE DETERMINES ARTICLE RELEVANCE")
    print(f"{'='*60}")

    # Test multiple tickers to understand the pattern
    test_tickers = ['AAPL', 'TSLA', 'MSFT', 'AMZN', 'GOOGL']

    for ticker in test_tickers:
        try:
            stock = yf.Ticker(ticker)
            news = stock.news

            if not news:
                continue

            print(f"\n--- {ticker} NEWS ANALYSIS ---")

            # Get company info
            try:
                info = stock.info
                company_name = info.get('longName', '')
                short_name = info.get('shortName', '')
                sector = info.get('sector', '')
                industry = info.get('industry', '')
            except:
                company_name = short_name = sector = industry = 'N/A'

            print(f"Company: {company_name}")
            print(f"Short Name: {short_name}")
            print(f"Sector: {sector}")
            print(f"Industry: {industry}")

            # Analyze first 3 articles
            for i, article in enumerate(news[:3]):
                content = article.get('content', article)
                title = content.get('title', '')
                summary = content.get('summary', '') or content.get('description', '')

                print(f"\nArticle {i+1}: {title[:80]}...")

                # Check various mentions
                text_to_check = f"{title} {summary}".lower()

                mentions = {
                    'ticker': ticker.lower() in text_to_check,
                    'company_name': company_name.lower() in text_to_check if company_name != 'N/A' else False,
                    'short_name': short_name.lower() in text_to_check if short_name != 'N/A' else False,
                    'sector': sector.lower() in text_to_check if sector != 'N/A' else False,
                    'industry': industry.lower() in text_to_check if industry != 'N/A' else False
                }

                mentioned = [key for key, value in mentions.items() if value]
                if mentioned:
                    print(f"  ✅ Mentions: {', '.join(mentioned)}")
                else:
                    print(f"  ❌ No direct mentions found")
                    # Check for related terms
                    related_terms = []
                    if 'tech' in text_to_check or 'technology' in text_to_check:
                        related_terms.append('tech/technology')
                    if 'stock' in text_to_check or 'market' in text_to_check:
                        related_terms.append('stock/market')
                    if 'magnificent' in text_to_check:
                        related_terms.append('magnificent 7')

                    if related_terms:
                        print(f"  🔍 Related terms: {', '.join(related_terms)}")

        except Exception as e:
            print(f"❌ Error with {ticker}: {e}")
            continue


def main():
    parser = argparse.ArgumentParser(description='Explore yfinance news data structure')
    parser.add_argument('--ticker', '-t', default='AAPL',
                       help='Ticker to explore (default: AAPL)')
    parser.add_argument('--compare', '-c', nargs='+',
                       default=['AAPL', 'MSFT', 'GOOGL'],
                       help='Tickers to compare (default: AAPL MSFT GOOGL)')
    parser.add_argument('--max-articles', '-m', type=int, default=5,
                       help='Maximum articles to show per ticker (default: 5)')
    parser.add_argument('--attribution', '-a', action='store_true',
                       help='Analyze news attribution for ticker')
    parser.add_argument('--deep-dive', '-d', action='store_true',
                       help='Deep dive into attribution mechanism')
    parser.add_argument('--all', action='store_true',
                       help='Run all analyses')

    args = parser.parse_args()

    print("🔍 YFINANCE NEWS EXPLORER")
    print("=" * 60)
    print("This script explores how yfinance associates news with stock tickers")

    if args.all:
        # Run all analyses
        explore_ticker_news(args.ticker, args.max_articles)
        compare_ticker_news(args.compare)
        analyze_news_attribution(args.ticker)
        deep_dive_attribution()
    else:
        # Single ticker exploration
        explore_ticker_news(args.ticker, args.max_articles)

        if args.attribution:
            analyze_news_attribution(args.ticker)

        if args.deep_dive:
            deep_dive_attribution()

        if len(args.compare) > 1:
            compare_ticker_news(args.compare)


if __name__ == "__main__":
    main()

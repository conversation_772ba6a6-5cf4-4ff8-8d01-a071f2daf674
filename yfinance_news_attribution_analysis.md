# YFinance News Attribution Analysis

## Summary of Findings

Based on our exploration of the `yfinance.news()` function, here's how Yahoo Finance determines which articles are associated with specific stock tickers:

## Key Findings

### 1. **News Attribution is NOT Always Direct**
- Many articles returned for a specific ticker **do not explicitly mention** the ticker symbol or company name
- For example, when searching for AAPL news, we found articles about:
  - General market trends ("Magnificent 7" rally)
  - Spotify (competitor analysis)
  - Trump's mobile phone service
  - General tech sector news

### 2. **Yahoo Finance Uses Algorithmic Association**
Yahoo Finance appears to use several methods to associate news with tickers:

#### **Direct Mentions** (Most Reliable)
- Articles that explicitly mention the ticker symbol (e.g., "TSLA", "AAPL")
- Articles that mention the company name (e.g., "Tesla", "Apple")

#### **Sector/Industry Association** (Moderately Reliable)
- Articles about the broader sector (e.g., "Technology" for AAPL, MSFT, GOOGL)
- Industry-related news (e.g., "Auto Manufacturers" for TSLA)

#### **Thematic/Contextual Association** (Least Reliable)
- Articles about market trends that affect large-cap stocks
- "Magnificent 7" articles appear for all major tech stocks
- Competitor analysis (Spotify news appears for Apple)
- Supply chain or regulatory news that might affect the company

### 3. **Significant Article Overlap Between Tickers**
- The same article often appears for multiple tickers
- Example: "The 2025 stock market rally is about more than just the 'Magnificent 7'" appears for AAPL, MSFT, and GOOGL
- This suggests Yahoo Finance casts a wide net for potentially relevant news

### 4. **Attribution Quality Varies by Ticker**
From our analysis of 10 articles per ticker:

| Ticker | Direct Mentions | Company Name | Attribution Quality |
|--------|----------------|--------------|-------------------|
| AAPL   | 3/10          | 0/10         | Mixed             |
| TSLA   | 4/10          | 0/10         | Better            |
| MSFT   | Variable      | Variable     | Mixed             |
| AMZN   | Variable      | Variable     | Mixed             |
| GOOGL  | Variable      | Variable     | Poor              |

## Data Structure Analysis

### Article Structure
Each article returned by `yfinance.Ticker(symbol).news` contains:

```python
{
    'content': {
        'title': 'Article headline',
        'summary': 'Article summary/description',
        'pubDate': 'Publication date',
        'provider': {'displayName': 'Source name'},
        'canonicalUrl': {'url': 'Article URL'},
        'clickThroughUrl': {'url': 'Click-through URL'},
        'contentType': 'STORY' or 'VIDEO',
        # ... other metadata
    },
    'id': 'unique_article_id'
}
```

### Key Fields for Analysis
- **title**: Primary headline
- **summary/description**: Article summary text
- **provider**: News source (Yahoo Finance, Bloomberg, etc.)
- **pubDate**: Publication timestamp
- **url**: Link to full article

## Implications for Financial Sentiment Analysis

### 1. **Article Relevance Filtering Needed**
Since not all articles are directly relevant to the ticker, our sentiment analyzer should:
- **Prioritize articles with direct ticker/company mentions**
- **Weight articles differently based on relevance**
- **Consider filtering out generic market news**

### 2. **Duplicate Detection is Critical**
- The same article appears for multiple tickers
- Our current deduplication by headline is working correctly
- This prevents the same sentiment from being counted multiple times

### 3. **Source Attribution in Our System**
In our current system (`data_fetcher.py`), we correctly:
- Associate each article with the ticker it was fetched for
- Add the ticker to the article metadata: `'ticker': ticker`
- Use this for ticker-specific sentiment analysis

### 4. **Potential Improvements**

#### **Enhanced Relevance Scoring**
```python
def calculate_relevance_score(article, ticker, company_info):
    score = 0
    text = f"{article['headline']} {article['text']}".lower()
    
    # Direct mentions (highest weight)
    if ticker.lower() in text:
        score += 10
    if company_info['name'].lower() in text:
        score += 8
    
    # Industry/sector mentions (medium weight)
    if company_info['sector'].lower() in text:
        score += 3
    
    # Generic market terms (low weight)
    if any(term in text for term in ['market', 'stock', 'trading']):
        score += 1
    
    return score
```

#### **Article Classification**
- **Highly Relevant**: Direct company/ticker mentions
- **Moderately Relevant**: Sector/industry news
- **Broadly Relevant**: General market news
- **Questionable**: No clear connection

## Recommendations

### 1. **For Current System**
- Keep the current approach but add relevance scoring
- Consider weighting sentiment based on relevance
- Add a flag to identify "high confidence" vs "low confidence" articles

### 2. **For Enhanced Analysis**
- Implement relevance filtering with configurable thresholds
- Add company name detection to improve attribution
- Consider using multiple news sources beyond yfinance

### 3. **For Transparency**
- Show users which articles are "directly relevant" vs "contextually relevant"
- Provide confidence scores for sentiment analysis
- Allow users to filter by relevance level

## Conclusion

Yahoo Finance's news attribution is **algorithmic and broad**, designed to capture any news that might be relevant to a stock. This is both a strength (comprehensive coverage) and a weakness (noise in the data).

For financial sentiment analysis, we need to be aware of this and implement our own relevance filtering to ensure we're analyzing truly relevant news for each ticker.

The current system works well but could be enhanced with relevance scoring and better attribution detection.

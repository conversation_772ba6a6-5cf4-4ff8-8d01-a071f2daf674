#!/usr/bin/env python3
"""
Financial Sentiment Analyzer with Government Policy Integration

A comprehensive tool for analyzing market sentiment from news sources and 
government policy announcements to provide trading insights.

Usage:
    python financial_analyzer.py [options]

Options:
    --help, -h              Show this help message
    --market-only           Run only market sentiment analysis
    --policy-only           Run only government policy analysis
    --sectors               Show detailed sector analysis
    --tickers               Show detailed ticker rankings
    --recommendations       Show analyst recommendations
    --indices               Show market indices performance
    --timeline              Show recent news timeline
    --dashboard             Show compact dashboard view (default)
    --enhanced              Show enhanced interactive dashboard with tabs
    --detailed              Show traditional detailed output format
    --quick                 Quick analysis (fewer sources)
    --verbose               Verbose output with debug info
    --live                  Live dashboard mode - updates every minute

Examples:
    python financial_analyzer.py                    # Full analysis (dashboard)
    python financial_analyzer.py --detailed         # Full analysis (detailed)
    python financial_analyzer.py --market-only      # Market sentiment only
    python financial_analyzer.py --policy-only      # Policy analysis only
    python financial_analyzer.py --quick            # Quick analysis
"""

import argparse
import sys
import os


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description='Financial Sentiment Analyzer with Government Policy Integration',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python financial_analyzer.py                    # Full analysis
  python financial_analyzer.py --market-only      # Market sentiment only
  python financial_analyzer.py --policy-only      # Policy analysis only
  python financial_analyzer.py --quick            # Quick analysis
        """
    )
    
    parser.add_argument('--market-only', action='store_true',
                       help='Run only market sentiment analysis')
    parser.add_argument('--policy-only', action='store_true',
                       help='Run only government policy analysis')
    parser.add_argument('--sectors', action='store_true',
                       help='Show detailed sector analysis')
    parser.add_argument('--tickers', action='store_true',
                       help='Show detailed ticker rankings')
    parser.add_argument('--recommendations', action='store_true',
                       help='Show analyst recommendations')
    parser.add_argument('--indices', action='store_true',
                       help='Show market indices performance')
    parser.add_argument('--timeline', action='store_true',
                       help='Show recent news timeline with sentiment scores (10-15 items)')
    parser.add_argument('--enhanced', action='store_true',
                       help='Show enhanced interactive dashboard with tabs and advanced features')
    parser.add_argument('--detailed', action='store_true',
                       help='Show detailed traditional output format')
    parser.add_argument('--quick', action='store_true',
                       help='Quick analysis (fewer sources)')
    parser.add_argument('--verbose', action='store_true',
                       help='Verbose output with debug info')

    return parser.parse_args()





def fetch_all_data(quick_mode=False):
    """Fetch all data"""
    # Import modules
    from data_fetcher import (fetch_market_news_parallel, fetch_government_news_parallel,
                             get_market_data_optimized)

    # Fetch market news
    news_data, news_stats = fetch_market_news_parallel(quick_mode=quick_mode)

    # Fetch government policy news
    government_data, policy_stats = fetch_government_news_parallel()

    # Get market data
    market_data = get_market_data_optimized()

    return news_data, news_stats, government_data, policy_stats, market_data


def analyze_all_data(news_data, government_data, market_data):
    """Analyze all data"""
    # Import analysis modules
    from sentiment_analyzer import (analyze_multi_ticker_sentiment, analyze_cross_ticker_sentiment, calculate_market_metrics,
                                  analyze_ticker_sentiment_optimized, analyze_sector_sentiment_optimized,
                                  rank_tickers_optimized, analyze_market_health_optimized)
    from policy_analyzer import analyze_policy_sentiment
    from data_fetcher import get_multiple_ticker_prices, get_multiple_ticker_current_prices

    # Initialize results
    sentiment_analysis = {}
    policy_analysis = {}
    sector_rankings = []
    ticker_rankings = []
    market_health = {}
    sentiment_scores = []
    sentiment_details = []
    multi_ticker_articles = []
    cross_ticker_analysis = {}
    price_changes = {}

    # Analyze market sentiment
    if news_data:
        # Enhanced sentiment analysis with multi-ticker detection
        sentiment_scores, sentiment_details, multi_ticker_articles = analyze_multi_ticker_sentiment(news_data)
        sentiment_analysis = calculate_market_metrics(sentiment_scores, sentiment_details)

        # Analyze cross-ticker sentiment patterns
        cross_ticker_analysis = analyze_cross_ticker_sentiment(multi_ticker_articles)

        # Analyze ticker sentiment
        ticker_sentiment = analyze_ticker_sentiment_optimized(news_data, sentiment_details)

        # Analyze sector sentiment
        sector_rankings = analyze_sector_sentiment_optimized(ticker_sentiment)

        # Rank tickers
        ticker_rankings = rank_tickers_optimized(ticker_sentiment)

    # Analyze government policy sentiment
    if government_data:
        policy_analysis = analyze_policy_sentiment(government_data)

    # Analyze market health
    if sentiment_analysis or policy_analysis:
        market_health = analyze_market_health_optimized(market_data, sentiment_analysis, policy_analysis)

    # Get price data
    current_prices = {}
    if ticker_rankings or sector_rankings:
        all_tickers = set()

        # Get prices for top tickers
        if ticker_rankings:
            top_sentiment_tickers = [ticker['ticker'] for ticker in ticker_rankings[:10]]
            all_tickers.update(top_sentiment_tickers)
            price_changes.update(get_multiple_ticker_prices(top_sentiment_tickers))

            # Also get the worst 5 tickers for dashboard display
            sorted_by_sentiment = sorted(ticker_rankings, key=lambda x: x['average_sentiment'])
            worst_tickers = [ticker['ticker'] for ticker in sorted_by_sentiment[:5]]
            all_tickers.update(worst_tickers)
            worst_price_changes = get_multiple_ticker_prices(worst_tickers)
            price_changes.update(worst_price_changes)

        # Get prices for top sector performers
        if sector_rankings:
            top_sector_tickers = [sector['top_ticker'] for sector in sector_rankings[:5]]
            all_tickers.update(top_sector_tickers)
            sector_price_changes = get_multiple_ticker_prices(top_sector_tickers)
            price_changes.update(sector_price_changes)

        # Get current prices for all tickers
        if all_tickers:
            current_prices = get_multiple_ticker_current_prices(list(all_tickers))

    return (sentiment_analysis, policy_analysis, market_health, sector_rankings,
            ticker_rankings, price_changes, current_prices, sentiment_scores, sentiment_details,
            multi_ticker_articles, cross_ticker_analysis)





def main():
    """Main function with modular execution based on command line arguments"""
    args = parse_arguments()
    
    # Import modules
    from data_fetcher import (fetch_market_news_parallel, fetch_government_news_parallel,
                             get_market_data_optimized, get_multiple_ticker_prices,
                             get_multiple_analyst_recommendations)
    from sentiment_analyzer import (analyze_sentiment_batch, calculate_market_metrics,
                                  analyze_ticker_sentiment_optimized, analyze_sector_sentiment_optimized,
                                  rank_tickers_optimized, analyze_market_health_optimized,
                                  analyze_multi_ticker_sentiment, analyze_cross_ticker_sentiment)
    from policy_analyzer import analyze_policy_sentiment
    from display_utils import (display_market_sentiment, display_policy_analysis,
                              display_high_impact_policy_news, display_sector_performance,
                              display_top_tickers, display_negative_tickers,
                              display_combined_analysis, display_recommendation,
                              display_market_indices, display_sentiment_ranked_timeline,
                              print_help)
    
    # Handle help (only show help if explicitly requested)
    if '--help' in sys.argv or '-h' in sys.argv:
        print_help()
        return

    # Handle enhanced dashboard mode
    if args.enhanced:
        try:
            from textual_dashboard import run_enhanced_textual_dashboard
            run_enhanced_textual_dashboard()
        except ImportError:
            print("❌ Textual not installed. Install with: pip install textual")
            print("Falling back to detailed view...")
        except Exception as e:
            print(f"Error in Enhanced Textual dashboard: {e}")
            print("Falling back to detailed view...")
            import traceback
            traceback.print_exc()
        else:
            return  # Exit if Enhanced dashboard ran successfully

    # Default to basic Textual dashboard unless detailed mode is requested
    elif not args.detailed:
        try:
            from textual_dashboard import run_textual_dashboard
            run_textual_dashboard()
        except ImportError:
            print("❌ Textual not installed. Install with: pip install textual")
            print("Falling back to detailed view...")
        except Exception as e:
            print(f"Error in Textual dashboard: {e}")
            print("Falling back to detailed view...")
            import traceback
            traceback.print_exc()
        else:
            return  # Exit if Textual dashboard ran successfully
    
    # Handle timeline-only mode
    if args.timeline:
        # Timeline mode: only fetch news and show timeline with sentiment scores
        print("🚀 FINANCIAL SENTIMENT ANALYZER - NEWS TIMELINE")
        print("=" * 70)

        news_data = fetch_market_news_parallel(quick_mode=args.quick)
        if not news_data:
            print("No news data available.")
            return

        # Analyze sentiment for timeline display
        print("Analyzing sentiment for timeline display...")
        sentiment_scores, sentiment_details = analyze_sentiment_batch(news_data)

        display_sentiment_ranked_timeline(news_data, sentiment_scores, sentiment_details, limit=15)
        return

    # Determine what to run based on arguments
    run_market = not args.policy_only
    run_policy = not args.market_only
    show_all = not any([args.sectors, args.tickers, args.recommendations,
                       args.indices])

    # We're in detailed mode if we get here
    print("🚀 FINANCIAL SENTIMENT ANALYZER WITH POLICY INTEGRATION")
    print("=" * 70)
    
    # Initialize variables
    news_data = []
    government_data = []
    market_data = {}
    sentiment_analysis = {}
    policy_analysis = {}
    ticker_sentiment = {}
    sector_rankings = []
    ticker_rankings = []
    market_health = {}
    sentiment_scores = []
    sentiment_details = []

    # Fetch market news if needed
    if run_market:
        news_data, _ = fetch_market_news_parallel(quick_mode=args.quick)
        if not news_data:
            print("No market news data available.")
            if args.market_only:
                return

    # Fetch government policy news if needed
    if run_policy:
        government_data, _ = fetch_government_news_parallel()
    
    # Get market data
    if run_market or show_all or args.indices:
        market_data = get_market_data_optimized()
    
    # Analyze market sentiment
    multi_ticker_articles = []
    cross_ticker_analysis = {}

    if run_market and news_data:
        # Enhanced sentiment analysis with multi-ticker detection
        sentiment_scores, sentiment_details, multi_ticker_articles = analyze_multi_ticker_sentiment(news_data)
        sentiment_analysis = calculate_market_metrics(sentiment_scores, sentiment_details)

        # Analyze cross-ticker sentiment patterns
        cross_ticker_analysis = analyze_cross_ticker_sentiment(multi_ticker_articles)

        # Analyze ticker sentiment
        ticker_sentiment = analyze_ticker_sentiment_optimized(news_data, sentiment_details)

        # Analyze sector sentiment
        sector_rankings = analyze_sector_sentiment_optimized(ticker_sentiment)

        # Rank tickers
        ticker_rankings = rank_tickers_optimized(ticker_sentiment)
    
    # Analyze government policy sentiment
    if run_policy and government_data:

        policy_analysis = analyze_policy_sentiment(government_data)
    
    # Analyze market health with policy integration
    if (run_market and sentiment_analysis) or (run_policy and policy_analysis):
        market_health = analyze_market_health_optimized(market_data, sentiment_analysis, policy_analysis)
    
    # Get price data for analysis
    price_changes = {}
    if ticker_rankings or sector_rankings:
        all_tickers = set()

        # Get prices for top tickers
        if ticker_rankings:
            top_sentiment_tickers = [ticker['ticker'] for ticker in ticker_rankings[:10]]
            all_tickers.update(top_sentiment_tickers)
            price_changes.update(get_multiple_ticker_prices(top_sentiment_tickers))

            # Also get the worst 5 tickers
            sorted_by_sentiment = sorted(ticker_rankings, key=lambda x: x['average_sentiment'])
            worst_tickers = [ticker['ticker'] for ticker in sorted_by_sentiment[:5]]
            all_tickers.update(worst_tickers)
            worst_price_changes = get_multiple_ticker_prices(worst_tickers)
            price_changes.update(worst_price_changes)

        # Get prices for top sector performers
        if sector_rankings:
            top_sector_tickers = [sector['top_ticker'] for sector in sector_rankings[:5]]
            all_tickers.update(top_sector_tickers)
            sector_price_changes = get_multiple_ticker_prices(top_sector_tickers)
            price_changes.update(sector_price_changes)

    # Display results in detailed view
    if run_market and sentiment_analysis:
        display_market_sentiment(sentiment_analysis)

        # Show multi-ticker analysis in detailed view
        if multi_ticker_articles and cross_ticker_analysis:
            from display_utils import display_multi_ticker_analysis
            display_multi_ticker_analysis(multi_ticker_articles, cross_ticker_analysis)

    if run_policy and policy_analysis:
        display_policy_analysis(policy_analysis)
        if policy_analysis.get('high_impact_articles'):
            display_high_impact_policy_news(policy_analysis)

    # Display sector analysis
    if (show_all or args.sectors) and sector_rankings:
        display_sector_performance(sector_rankings, price_changes)

    # Display ticker analysis
    if (show_all or args.tickers) and ticker_rankings:
        top_sentiment_recommendations = {}
        if show_all or args.recommendations:
            top_sentiment_tickers = [ticker['ticker'] for ticker in ticker_rankings[:5]]
            top_sentiment_recommendations = get_multiple_analyst_recommendations(top_sentiment_tickers)

        display_top_tickers(ticker_rankings, price_changes, top_sentiment_recommendations)

        # Show negative sentiment tickers
        negative_ticker_symbols = [ticker['ticker'] for ticker in ticker_rankings if ticker['average_sentiment'] < -0.05][:3]
        if negative_ticker_symbols:
            negative_prices = get_multiple_ticker_prices(negative_ticker_symbols)
            negative_recommendations = {}
            if show_all or args.recommendations:
                negative_recommendations = get_multiple_analyst_recommendations(negative_ticker_symbols)
            display_negative_tickers(ticker_rankings, negative_prices, negative_recommendations)

    # Display combined analysis
    if run_market and run_policy and market_health:
        display_combined_analysis(market_health)

    # Display recommendation
    if market_health:
        display_recommendation(market_health)

    # Display market indices
    if (show_all or args.indices) and market_data:
        display_market_indices(market_data)
    
    # Note: Timeline display is handled separately above if --timeline flag is used


if __name__ == "__main__":
    main()

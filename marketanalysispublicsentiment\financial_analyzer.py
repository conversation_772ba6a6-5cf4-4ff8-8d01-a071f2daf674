#!/usr/bin/env python3
"""
🚀 Financial Sentiment Analyzer - Enhanced Interactive Dashboard

A comprehensive tool for real-time market sentiment analysis with an advanced
interactive dashboard interface powered by Textual framework.

Features:
    📊 Overview Tab         Complete market summary with real-time sentiment analysis
    🏆 Tickers Tab          Interactive sortable table with detailed ticker analysis
    📰 News Tab             Tree-organized news by sentiment with trend charts
    🏛️ Policy Tab           Government policy analysis and impact assessment

Usage:
    python financial_analyzer.py [options]

Options:
    --quick                 Quick analysis mode (fewer data sources, faster startup)
    --verbose               Verbose output with debug information

Navigation:
    q                       Quit application
    r                       Manual refresh data
    f                       Toggle filter controls
    1-4                     Switch between tabs
    Ctrl+E                  Export data

Examples:
    python financial_analyzer.py                    # Full enhanced dashboard
    python financial_analyzer.py --quick            # Quick mode dashboard
    python financial_analyzer.py --verbose          # Dashboard with debug info
"""

import argparse
import sys


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description='🚀 Financial Sentiment Analyzer - Enhanced Interactive Dashboard',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Dashboard Features:
  📊 Overview Tab         Complete market summary with real-time sentiment analysis
  🏆 Tickers Tab          Interactive sortable table with detailed ticker analysis
  📰 News Tab             Tree-organized news by sentiment with trend charts
  🏛️ Policy Tab           Government policy analysis and impact assessment

Navigation:
  q                       Quit application
  r                       Manual refresh data
  f                       Toggle filter controls
  1-4                     Switch between tabs
  Ctrl+E                  Export data

Examples:
  python financial_analyzer.py                    # Full enhanced dashboard
  python financial_analyzer.py --quick            # Quick mode dashboard
  python financial_analyzer.py --verbose          # Dashboard with debug info
        """
    )

    parser.add_argument('--quick', action='store_true',
                       help='Quick analysis mode (fewer data sources, faster startup)')
    parser.add_argument('--verbose', action='store_true',
                       help='Verbose output with debug information')

    return parser.parse_args()





def fetch_all_data(quick_mode=False):
    """Fetch all data"""
    # Import modules
    from data_fetcher import (fetch_market_news_parallel, fetch_government_news_parallel,
                             get_market_data_optimized)

    # Fetch market news
    news_data, news_stats = fetch_market_news_parallel(quick_mode=quick_mode)

    # Fetch government policy news
    government_data, policy_stats = fetch_government_news_parallel()

    # Get market data
    market_data = get_market_data_optimized()

    return news_data, news_stats, government_data, policy_stats, market_data


def analyze_all_data(news_data, government_data, market_data):
    """Analyze all data"""
    # Import analysis modules
    from sentiment_analyzer import (analyze_multi_ticker_sentiment, analyze_cross_ticker_sentiment, calculate_market_metrics,
                                  analyze_ticker_sentiment_optimized, analyze_sector_sentiment_optimized,
                                  rank_tickers_optimized, analyze_market_health_optimized)
    from policy_analyzer import analyze_policy_sentiment
    from data_fetcher import get_multiple_ticker_prices, get_multiple_ticker_current_prices

    # Initialize results
    sentiment_analysis = {}
    policy_analysis = {}
    sector_rankings = []
    ticker_rankings = []
    market_health = {}
    sentiment_scores = []
    sentiment_details = []
    multi_ticker_articles = []
    cross_ticker_analysis = {}
    price_changes = {}

    # Analyze market sentiment
    if news_data:
        # Enhanced sentiment analysis with multi-ticker detection
        sentiment_scores, sentiment_details, multi_ticker_articles = analyze_multi_ticker_sentiment(news_data)
        sentiment_analysis = calculate_market_metrics(sentiment_scores, sentiment_details)

        # Analyze cross-ticker sentiment patterns
        cross_ticker_analysis = analyze_cross_ticker_sentiment(multi_ticker_articles)

        # Analyze ticker sentiment
        ticker_sentiment = analyze_ticker_sentiment_optimized(news_data, sentiment_details)

        # Analyze sector sentiment
        sector_rankings = analyze_sector_sentiment_optimized(ticker_sentiment)

        # Rank tickers
        ticker_rankings = rank_tickers_optimized(ticker_sentiment)

    # Analyze government policy sentiment
    if government_data:
        policy_analysis = analyze_policy_sentiment(government_data)

    # Analyze market health
    if sentiment_analysis or policy_analysis:
        market_health = analyze_market_health_optimized(market_data, sentiment_analysis, policy_analysis)

    # Get price data for ALL analyzed tickers (not just top performers)
    current_prices = {}
    if ticker_rankings:
        # Get ALL tickers that have been analyzed for sentiment
        all_analyzed_tickers = [ticker['ticker'] for ticker in ticker_rankings]

        # Fetch price changes for all analyzed tickers
        price_changes.update(get_multiple_ticker_prices(all_analyzed_tickers))

        # Fetch current prices for all analyzed tickers
        current_prices = get_multiple_ticker_current_prices(all_analyzed_tickers)

        # Also get prices for sector performers if available
        if sector_rankings:
            sector_tickers = [sector['top_ticker'] for sector in sector_rankings[:5]]
            # Add any sector tickers that weren't already included
            additional_tickers = [t for t in sector_tickers if t not in all_analyzed_tickers]
            if additional_tickers:
                additional_price_changes = get_multiple_ticker_prices(additional_tickers)
                price_changes.update(additional_price_changes)
                additional_current_prices = get_multiple_ticker_current_prices(additional_tickers)
                current_prices.update(additional_current_prices)

    return (sentiment_analysis, policy_analysis, market_health, sector_rankings,
            ticker_rankings, price_changes, current_prices, sentiment_scores, sentiment_details,
            multi_ticker_articles, cross_ticker_analysis)





def main():
    """Main function - launches the enhanced interactive dashboard"""
    args = parse_arguments()

    # Set up verbose mode if requested
    if args.verbose:
        print("🚀 FINANCIAL SENTIMENT ANALYZER - Enhanced Interactive Dashboard")
        print("=" * 70)
        print("🔧 Verbose mode enabled")
        print("⚡ Quick mode:", "ON" if args.quick else "OFF")
        print("📊 Loading enhanced dashboard...")
        print()

    # Launch the enhanced dashboard
    try:
        from textual_dashboard import run_enhanced_textual_dashboard

        # Pass configuration to the dashboard
        if args.verbose:
            print("✅ Enhanced dashboard loaded successfully")
            print("🎯 Starting interactive interface...")
            print()

        run_enhanced_textual_dashboard(quick_mode=args.quick, verbose=args.verbose)

    except ImportError:
        print("❌ Textual framework not installed!")
        print("📦 Install with: pip install textual")
        print("💡 This is required for the interactive dashboard interface.")
        sys.exit(1)

    except Exception as e:
        print(f"❌ Error launching enhanced dashboard: {e}")
        if args.verbose:
            import traceback
            print("\n🔍 Detailed error information:")
            traceback.print_exc()
        print("\n💡 Try running with --verbose for more details")
        sys.exit(1)


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Advanced Interactive Textual Dashboard for Financial Sentiment Analyzer

This showcases Textual's full potential with:
- Interactive widgets and controls
- Tabbed interfaces for different views
- Real-time charts and data tables
- Modal dialogs for detailed analysis
- Keyboard shortcuts and mouse interactions
- Rich styling and responsive layouts
"""

from textual.app import App, ComposeResult
from textual.containers import Container, Horizontal, Vertical, ScrollableContainer, Grid
from textual.widgets import (
    Header, Footer, Static, Button, TabbedContent, TabPane,
    DataTable, ProgressBar, Input, Select, Label, Checkbox,
    Tree, ListView, ListItem, Digits, Log
)
from textual.reactive import reactive
from textual.binding import Binding
from textual.screen import ModalScreen
from textual.message import Message
from rich.console import Console
from rich.table import Table
from rich.text import Text
from rich.panel import Panel
from rich.align import Align
from rich.progress import Progress, SpinnerColumn, TextColumn
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional


class TickerDetailModal(ModalScreen):
    """Modal screen showing detailed ticker analysis"""
    
    def __init__(self, ticker_data: Dict[str, Any], **kwargs):
        super().__init__(**kwargs)
        self.ticker_data = ticker_data
    
    def compose(self) -> ComposeResult:
        ticker = self.ticker_data.get('ticker', 'N/A')
        with Container(id="ticker-modal"):
            yield Label(f"📊 Detailed Analysis: {ticker}", id="modal-title")
            
            # Create detailed table
            table = DataTable()
            table.add_columns("Metric", "Value", "Trend")
            table.add_row("Current Price", f"${self.ticker_data.get('price', 0):.2f}", "📈")
            table.add_row("Sentiment Score", f"{self.ticker_data.get('sentiment', 0):.3f}", "🟢")
            table.add_row("Article Count", str(self.ticker_data.get('articles', 0)), "📊")
            table.add_row("Sector", self.ticker_data.get('sector', 'N/A'), "🏭")
            yield table
            
            with Horizontal():
                yield Button("Close", variant="primary", id="close-modal")
                yield Button("Add to Watchlist", variant="success", id="add-watchlist")
    
    def on_button_pressed(self, event: Button.Pressed) -> None:
        if event.button.id == "close-modal":
            self.dismiss()
        elif event.button.id == "add-watchlist":
            # Add to watchlist logic here
            self.dismiss()


class InteractiveTickerTable(DataTable):
    """Interactive data table for tickers with sorting and filtering"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.cursor_type = "row"
        self.zebra_stripes = True
        
    def on_mount(self) -> None:
        self.add_columns("Rank", "Ticker", "Price", "Change", "Sentiment", "Articles")
        
    def update_data(self, ticker_rankings: List[Dict], price_changes: Dict, current_prices: Dict):
        """Update table with new ticker data"""
        self.clear()
        
        for i, ticker in enumerate(ticker_rankings[:20], 1):
            ticker_symbol = ticker['ticker']
            price_change = price_changes.get(ticker_symbol, 0.0)
            current_price = current_prices.get(ticker_symbol, 0.0)
            
            # Color coding for sentiment
            sentiment_score = ticker['overall_score']
            if sentiment_score > 0.3:
                sentiment_color = "green"
            elif sentiment_score > 0.1:
                sentiment_color = "yellow"
            else:
                sentiment_color = "red"
            
            # Price change emoji
            price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"
            
            self.add_row(
                str(i),
                ticker_symbol,
                f"${current_price:.2f}",
                f"{price_emoji} {price_change:+.1f}%",
                Text(f"{sentiment_score:.3f}", style=sentiment_color),
                str(ticker.get('total_articles', 0))
            )
    
    def on_data_table_row_selected(self, event: DataTable.RowSelected) -> None:
        """Handle row selection - show detailed modal"""
        row_data = self.get_row_at(event.cursor_row)
        if row_data:
            ticker_symbol = str(row_data[1])  # Ticker column
            ticker_data = {
                'ticker': ticker_symbol,
                'price': float(str(row_data[2]).replace('$', '')),
                'sentiment': float(str(row_data[4]).plain),
                'articles': int(str(row_data[5])),
                'sector': 'Technology'  # Would get from actual data
            }
            self.app.push_screen(TickerDetailModal(ticker_data))


class RealTimeChart(Static):
    """Real-time sentiment chart using Sparkline"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.sentiment_history = []
        
    def update_sentiment(self, sentiment_score: float):
        """Add new sentiment data point"""
        self.sentiment_history.append(sentiment_score)
        if len(self.sentiment_history) > 50:  # Keep last 50 points
            self.sentiment_history.pop(0)
        
        # Create simple chart representation
        if len(self.sentiment_history) > 1:
            # Create ASCII chart
            chart_data = "".join("▁▂▃▄▅▆▇█"[min(7, max(0, int((val + 1) * 4)))] for val in self.sentiment_history[-20:])
            chart_text = f"📈 Sentiment Trend\n{chart_data}\nCurrent: {self.sentiment_history[-1]:+.3f}"
            self.update(Panel(chart_text, title="📈 Live Chart"))


class FilterControls(Container):
    """Interactive filter controls"""
    
    def compose(self) -> ComposeResult:
        yield Label("🔍 Filters & Controls")
        
        with Horizontal():
            yield Select([
                ("All Sectors", "all"),
                ("Technology", "tech"),
                ("Financial", "finance"),
                ("Healthcare", "health")
            ], prompt="Select Sector", id="sector-filter")
            
            yield Select([
                ("All Sentiment", "all"),
                ("Positive Only", "positive"),
                ("Negative Only", "negative")
            ], prompt="Sentiment Filter", id="sentiment-filter")
        
        with Horizontal():
            yield Checkbox("Auto-refresh", value=True, id="auto-refresh")
            yield Button("🔄 Refresh Now", variant="primary", id="manual-refresh")
            yield Button("📊 Export Data", variant="success", id="export-data")


class NewsTreeView(Tree):
    """Interactive tree view for news articles"""
    
    def __init__(self, **kwargs):
        super().__init__("📰 Recent News", **kwargs)
        
    def update_news(self, news_data: List[Dict], sentiment_scores: List[float]):
        """Update tree with news data"""
        self.clear()
        
        # Group by sentiment
        positive_node = self.root.add("🟢 Positive News")
        neutral_node = self.root.add("🟡 Neutral News") 
        negative_node = self.root.add("🔴 Negative News")
        
        for i, (article, sentiment) in enumerate(zip(news_data[:15], sentiment_scores[:15])):
            headline = article['headline'][:60] + "..." if len(article['headline']) > 60 else article['headline']
            ticker = article.get('ticker', 'N/A')
            time_ago = article.get('time_ago', 'Unknown')
            
            node_text = f"[{time_ago}] {ticker}: {headline}"
            
            if sentiment > 0.1:
                positive_node.add_leaf(node_text)
            elif sentiment < -0.1:
                negative_node.add_leaf(node_text)
            else:
                neutral_node.add_leaf(node_text)


class AdvancedFinancialDashboard(App):
    """Advanced interactive financial dashboard showcasing Textual's full potential"""
    
    CSS = """
    #ticker-modal {
        align: center middle;
        width: 80;
        height: 20;
        background: $surface;
        border: thick $primary;
    }
    
    #modal-title {
        dock: top;
        height: 3;
        content-align: center middle;
        text-style: bold;
        background: $primary;
        color: $text;
    }
    
    .data-table {
        height: 1fr;
    }
    
    .chart-container {
        height: 8;
        border: solid $primary;
    }
    
    .controls-panel {
        height: 6;
        border: solid $secondary;
    }
    
    .status-bar {
        dock: bottom;
        height: 1;
        background: $primary;
        color: $text;
    }
    
    TabbedContent {
        height: 1fr;
    }
    
    TabPane {
        padding: 1;
    }
    """
    
    BINDINGS = [
        Binding("q", "quit", "Quit"),
        Binding("r", "refresh", "Refresh"),
        Binding("f", "toggle_filter", "Toggle Filters"),
        Binding("1", "switch_tab('overview')", "Overview"),
        Binding("2", "switch_tab('tickers')", "Tickers"),
        Binding("3", "switch_tab('news')", "News"),
        Binding("4", "switch_tab('analysis')", "Analysis"),
    ]
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "🚀 Advanced Financial Sentiment Analyzer"
        self.sub_title = "Interactive Real-time Market Analysis"
        
    def compose(self) -> ComposeResult:
        yield Header()
        
        with TabbedContent(initial="overview"):
            # Overview Tab
            with TabPane("📊 Overview", id="overview"):
                with Vertical():
                    yield FilterControls(classes="controls-panel")
                    
                    with Horizontal():
                        with Vertical():
                            yield RealTimeChart(classes="chart-container")
                            yield Static("📈 Market Summary\n\nSentiment: Positive\nTrend: Bullish\nVolatility: Low", 
                                       classes="chart-container")
                        
                        with Vertical():
                            yield Digits("0.127", id="sentiment-display")
                            yield Label("Current Market Sentiment")
                            yield ProgressBar(total=100, progress=65, id="sentiment-progress")
            
            # Interactive Tickers Tab
            with TabPane("🏆 Tickers", id="tickers"):
                yield InteractiveTickerTable(classes="data-table")
            
            # News Tree Tab  
            with TabPane("📰 News", id="news"):
                with Horizontal():
                    yield NewsTreeView(id="news-tree")
                    with Vertical():
                        yield Log(id="news-details")
                        yield Button("📖 Read Full Article", id="read-article")
            
            # Advanced Analysis Tab
            with TabPane("🔬 Analysis", id="analysis"):
                with Grid():
                    yield Static("🔄 Multi-Ticker Conflicts\n\nNo conflicts detected", classes="chart-container")
                    yield Static("🔗 Ticker Correlations\n\nAAPL-MSFT: 0.85\nTSLA-NVDA: 0.72", classes="chart-container")
                    yield Static("📊 Sector Performance\n\nTech: +2.1%\nFinance: -0.8%", classes="chart-container")
                    yield Static("🏛️ Policy Impact\n\nNeutral influence\nScore: +0.05", classes="chart-container")
        
        yield Static("🔄 Auto-refresh: ON | Last update: " + datetime.now().strftime("%H:%M:%S"), 
                    classes="status-bar")
        yield Footer()
    
    def on_mount(self) -> None:
        """Initialize the dashboard with data"""
        self.set_timer(60, self.refresh_data)  # Auto-refresh every 60 seconds
        self.refresh_data()
    
    def refresh_data(self) -> None:
        """Refresh all dashboard data"""
        # This would fetch real data
        self.update_status("🔄 Refreshing data...")
        
        # Simulate data update
        self.call_later(self.update_status, "✅ Data updated - " + datetime.now().strftime("%H:%M:%S"))
    
    def update_status(self, message: str) -> None:
        """Update status bar"""
        status_bar = self.query_one(".status-bar", Static)
        status_bar.update(message)
    
    def action_refresh(self) -> None:
        """Manual refresh action"""
        self.refresh_data()
    
    def action_toggle_filter(self) -> None:
        """Toggle filter visibility"""
        filters = self.query_one(".controls-panel")
        filters.display = not filters.display
    
    def action_switch_tab(self, tab_id: str) -> None:
        """Switch to specific tab"""
        tabbed_content = self.query_one(TabbedContent)
        tabbed_content.active = tab_id


def run_advanced_textual_dashboard():
    """Run the advanced interactive dashboard"""
    app = AdvancedFinancialDashboard()
    app.run()


if __name__ == "__main__":
    run_advanced_textual_dashboard()

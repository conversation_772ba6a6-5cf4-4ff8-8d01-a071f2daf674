#!/usr/bin/env python3
"""
Test script to demonstrate multi-ticker sentiment analysis functionality
"""

import sys
import os
sys.path.append('marketanalysispublicsentiment')

from sentiment_analyzer import analyze_multi_ticker_sentiment, analyze_cross_ticker_sentiment


def test_multi_ticker_sentiment():
    """Test the multi-ticker sentiment analysis with sample data"""
    
    # Sample news articles with multiple ticker mentions
    sample_articles = [
        {
            'headline': 'Apple and Microsoft report strong earnings while Tesla struggles',
            'text': 'AAPL and MSFT both beat expectations with strong quarterly results. However, TSLA disappointed investors with lower than expected deliveries.',
            'ticker': 'AAPL',  # Original ticker this was fetched for
            'time_ago': '2 hours ago',
            'url': 'https://example.com/news1'
        },
        {
            'headline': 'Tech sector rally led by NVIDIA and AMD',
            'text': 'NVDA continues its AI dominance while AMD gains market share in data center chips. Both stocks are up significantly.',
            'ticker': 'NVDA',
            'time_ago': '1 hour ago',
            'url': 'https://example.com/news2'
        },
        {
            'headline': 'Amazon vs Walmart: E-commerce battle intensifies',
            'text': 'AMZN faces increased competition from WMT in the online retail space. Amazon stock down while Walmart shows resilience.',
            'ticker': 'AMZN',
            'time_ago': '3 hours ago',
            'url': 'https://example.com/news3'
        },
        {
            'headline': 'Banking sector mixed as JPM rises but GS falls',
            'text': 'JPMorgan Chase (JPM) reported solid results, but Goldman Sachs (GS) disappointed with trading revenue decline.',
            'ticker': 'JPM',
            'time_ago': '4 hours ago',
            'url': 'https://example.com/news4'
        },
        {
            'headline': 'Market rally continues with broad participation',
            'text': 'The S&P 500 continues its upward trend with most sectors participating in the rally.',
            'ticker': 'SPY',
            'time_ago': '30 minutes ago',
            'url': 'https://example.com/news5'
        }
    ]
    
    print("🔍 TESTING MULTI-TICKER SENTIMENT ANALYSIS")
    print("=" * 60)
    
    # Run the multi-ticker sentiment analysis
    sentiment_scores, sentiment_details, multi_ticker_articles = analyze_multi_ticker_sentiment(sample_articles)
    
    print(f"\n📊 ANALYSIS RESULTS:")
    print(f"Total articles analyzed: {len(sample_articles)}")
    print(f"Multi-ticker articles found: {len(multi_ticker_articles)}")
    
    # Show detailed results for multi-ticker articles
    print(f"\n🔄 MULTI-TICKER ARTICLES:")
    print("-" * 50)
    
    for i, article_data in enumerate(multi_ticker_articles, 1):
        article = article_data['article']
        mentioned_tickers = article_data['mentioned_tickers']
        ticker_sentiments = article_data['ticker_sentiments']
        
        print(f"\n{i}. {article['headline']}")
        print(f"   Original ticker: {article['ticker']}")
        print(f"   Mentioned tickers: {', '.join(mentioned_tickers)}")
        print(f"   Overall sentiment: {article_data['overall_sentiment']:+.3f}")
        
        # Show sentiment for each mentioned ticker
        for ticker in mentioned_tickers:
            if ticker in ticker_sentiments:
                sentiment = ticker_sentiments[ticker]
                print(f"   • {ticker}: {sentiment['sentiment_category']} ({sentiment['sentiment_score']:+.3f})")
                if sentiment['context_snippets']:
                    snippet = sentiment['context_snippets'][0][:100] + "..." if len(sentiment['context_snippets'][0]) > 100 else sentiment['context_snippets'][0]
                    print(f"     Context: \"{snippet}\"")
    
    # Analyze cross-ticker sentiment patterns
    cross_ticker_analysis = analyze_cross_ticker_sentiment(multi_ticker_articles)
    
    print(f"\n🔗 CROSS-TICKER ANALYSIS:")
    print("-" * 50)
    print(f"Summary: {cross_ticker_analysis['summary']}")
    
    # Show sentiment conflicts
    if cross_ticker_analysis['sentiment_conflicts']:
        print(f"\n⚠️  SENTIMENT CONFLICTS:")
        for conflict in cross_ticker_analysis['sentiment_conflicts']:
            print(f"• {conflict['headline'][:60]}...")
            print(f"  Positive: {', '.join(conflict['positive_tickers'])}")
            print(f"  Negative: {', '.join(conflict['negative_tickers'])}")
    
    # Show ticker pairs
    if cross_ticker_analysis['ticker_pairs']:
        print(f"\n📈 TICKER PAIRS:")
        for pair, data in list(cross_ticker_analysis['ticker_pairs'].items())[:5]:
            print(f"• {pair}: {data['count']} articles")
            for pattern in data['sentiment_patterns'][-2:]:  # Show last 2 patterns
                print(f"  - {pattern}")
    
    print(f"\n✅ Test completed successfully!")


if __name__ == "__main__":
    test_multi_ticker_sentiment()

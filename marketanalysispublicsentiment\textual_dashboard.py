#!/usr/bin/env python3
"""
Enhanced Interactive Textual Dashboard for Financial Sentiment Analyzer
Combines real-time data with advanced interactive features
"""

from textual.app import App, ComposeResult
from textual.containers import Container, Horizontal, Vertical, ScrollableContainer, Grid
from textual.widgets import (
    Header, Footer, Static, DataTable, Label, ProgressBar, Button,
    TabbedContent, TabPane, Input, Select, Checkbox, Tree, ListView,
    ListItem, Digits, Log, Switch
)
from textual.reactive import reactive, var
from textual.binding import Binding
from textual.screen import ModalScreen
from textual.message import Message
from textual import events
from rich.text import Text
from rich.table import Table
from rich.panel import Panel
from rich.console import Console
from rich.align import Align
from rich.progress import Progress, SpinnerColumn, TextColumn
from datetime import datetime
import asyncio
from typing import Dict, List, Any, Optional


class TickerDetailModal(ModalScreen):
    """Modal screen showing detailed ticker analysis"""

    def __init__(self, ticker_data: Dict[str, Any], **kwargs):
        super().__init__(**kwargs)
        self.ticker_data = ticker_data

    def compose(self) -> ComposeResult:
        ticker = self.ticker_data.get('ticker', 'N/A')
        with Container(id="ticker-modal"):
            yield Label(f"📊 Detailed Analysis: {ticker}", id="modal-title")

            # Create detailed table
            table = DataTable()
            table.add_columns("Metric", "Value", "Trend")
            table.add_row("Current Price", f"${self.ticker_data.get('price', 0):.2f}", "📈")
            table.add_row("Sentiment Score", f"{self.ticker_data.get('sentiment', 0):.3f}", "🟢")
            table.add_row("Article Count", str(self.ticker_data.get('articles', 0)), "📊")
            table.add_row("Positive %", f"{self.ticker_data.get('positive_pct', 0):.1f}%", "🟢")
            table.add_row("Negative %", f"{self.ticker_data.get('negative_pct', 0):.1f}%", "🔴")
            table.add_row("Sector", self.ticker_data.get('sector', 'N/A'), "🏭")
            yield table

            with Horizontal():
                yield Button("Close", variant="primary", id="close-modal")
                yield Button("View News", variant="success", id="view-news")

    def on_button_pressed(self, event: Button.Pressed) -> None:
        if event.button.id == "close-modal":
            self.dismiss()
        elif event.button.id == "view-news":
            # Could implement news filtering for this ticker
            self.dismiss()


class ArticleDetailModal(ModalScreen):
    """Enhanced modal screen showing detailed article analysis with hyperlinks"""

    def __init__(self, article_data: Dict[str, Any], **kwargs):
        super().__init__(**kwargs)
        self.article_data = article_data

    def compose(self) -> ComposeResult:
        with Container(id="article-modal"):
            yield Label("📰 Article Details", id="modal-title")

            # Create rich article content with proper formatting
            table = Table.grid(padding=1)
            table.add_column("Field", style="bold cyan", width=15)
            table.add_column("Value", width=60)

            # Article details
            headline = self.article_data.get('headline', 'N/A')
            time_ago = self.article_data.get('time_ago', 'Unknown')
            sentiment = self.article_data.get('sentiment', 0)
            category = self.article_data.get('category', 'N/A')
            url = self.article_data.get('url', '')

            # Sentiment color coding
            if sentiment > 0.1:
                sentiment_style = "green"
                sentiment_emoji = "🟢"
            elif sentiment < -0.1:
                sentiment_style = "red"
                sentiment_emoji = "🔴"
            else:
                sentiment_style = "yellow"
                sentiment_emoji = "🟡"

            table.add_row("📰 Headline:", headline[:50] + "..." if len(headline) > 50 else headline)
            table.add_row("⏰ Time:", time_ago)
            table.add_row("📊 Sentiment:", Text(f"{sentiment_emoji} {sentiment:.3f}", style=sentiment_style))
            table.add_row("🏷️ Category:", category)

            # Mentioned tickers
            if 'mentioned_tickers' in self.article_data and self.article_data['mentioned_tickers']:
                tickers = ', '.join(self.article_data['mentioned_tickers'][:5])
                if len(self.article_data['mentioned_tickers']) > 5:
                    tickers += f" +{len(self.article_data['mentioned_tickers']) - 5} more"
                table.add_row("🎯 Tickers:", tickers)

            # URL with hyperlink if available
            if url:
                # Create clickable hyperlink using OSC 8 escape sequences
                hyperlink = f"\033]8;;{url}\033\\🔗 Click to open article\033]8;;\033\\"
                table.add_row("🌐 Link:", hyperlink)
            else:
                table.add_row("🌐 Link:", "Not available")

            yield Static(table, id="article-content")

            with Horizontal():
                yield Button("Close", variant="primary", id="close-modal")
                if url:
                    yield Button("🌐 Open URL", variant="success", id="open-url")

    def on_button_pressed(self, event: Button.Pressed) -> None:
        if event.button.id == "close-modal":
            self.dismiss()
        elif event.button.id == "open-url":
            url = self.article_data.get('url', '')
            if url:
                # Open URL in default browser
                import webbrowser
                try:
                    webbrowser.open(url)
                    self.app.notify(f"Opening article in browser...", severity="information")
                except Exception as e:
                    self.app.notify(f"Could not open URL: {str(e)}", severity="error")
            self.dismiss()


class FilterControls(Container):
    """Interactive filter controls"""

    def compose(self) -> ComposeResult:
        yield Label("🔍 Filters & Controls")

        with Horizontal():
            yield Select([
                ("All Sectors", "all"),
                ("Technology", "tech"),
                ("Financial", "finance"),
                ("Healthcare", "health"),
                ("Energy", "energy"),
                ("Consumer", "consumer")
            ], prompt="Select Sector", id="sector-filter")

            yield Select([
                ("All Sentiment", "all"),
                ("Positive Only", "positive"),
                ("Negative Only", "negative"),
                ("Neutral Only", "neutral")
            ], prompt="Sentiment Filter", id="sentiment-filter")

        with Horizontal():
            yield Switch(value=True, id="auto-refresh")
            yield Label("Auto-refresh")
            yield Button("🔄 Refresh Now", variant="primary", id="manual-refresh")
            yield Button("📊 Export Data", variant="success", id="export-data")


class InteractiveTickerTable(DataTable):
    """Interactive data table for tickers with sorting and filtering"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.cursor_type = "row"
        self.zebra_stripes = True
        self.show_cursor = True

    def on_mount(self) -> None:
        self.add_columns("Rank", "Ticker", "Price", "Change", "Sentiment", "Articles", "Sector")

    def update_data(self, ticker_rankings: List[Dict], price_changes: Dict, current_prices: Dict):
        """Update table with new ticker data"""
        self.clear()

        for i, ticker in enumerate(ticker_rankings[:25], 1):
            ticker_symbol = ticker['ticker']
            price_change = price_changes.get(ticker_symbol, 0.0)
            current_price = current_prices.get(ticker_symbol, 0.0)

            # Color coding for sentiment
            sentiment_score = ticker['overall_score']
            if sentiment_score > 0.3:
                sentiment_color = "green"
            elif sentiment_score > 0.1:
                sentiment_color = "yellow"
            elif sentiment_score > -0.1:
                sentiment_color = "white"
            else:
                sentiment_color = "red"

            # Price change emoji and color
            if price_change > 0:
                price_emoji = "📈"
                price_color = "green"
            elif price_change < 0:
                price_emoji = "📉"
                price_color = "red"
            else:
                price_emoji = "➡️"
                price_color = "white"

            self.add_row(
                str(i),
                ticker_symbol,
                f"${current_price:.2f}",
                Text(f"{price_emoji} {price_change:+.1f}%", style=price_color),
                Text(f"{sentiment_score:.3f}", style=sentiment_color),
                str(ticker.get('total_articles', 0)),
                ticker.get('sector', 'N/A')[:8]  # Truncate sector name
            )

    def on_data_table_row_selected(self, event: DataTable.RowSelected) -> None:
        """Handle row selection - show detailed modal"""
        row_data = self.get_row_at(event.cursor_row)
        if row_data:
            ticker_symbol = str(row_data[1])
            ticker_data = {
                'ticker': ticker_symbol,
                'price': float(str(row_data[2]).replace('$', '')),
                'sentiment': float(str(row_data[4]).plain),
                'articles': int(str(row_data[5])),
                'sector': str(row_data[6])
            }
            self.app.push_screen(TickerDetailModal(ticker_data))


class RealTimeChart(Static):
    """Real-time sentiment chart using ASCII/Unicode characters"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.sentiment_history = []
        self.border_title = "📈 Sentiment Trend"

    def update_sentiment(self, sentiment_score: float):
        """Add new sentiment data point"""
        self.sentiment_history.append(sentiment_score)
        if len(self.sentiment_history) > 50:  # Keep last 50 points
            self.sentiment_history.pop(0)

        self._render_chart()

    def _render_chart(self):
        """Render the ASCII chart"""
        if len(self.sentiment_history) < 2:
            self.update("📈 Collecting data...")
            return

        # Create simple sparkline chart
        chart_chars = "▁▂▃▄▅▆▇█"
        chart_data = []

        # Normalize data to 0-7 range for chart characters
        min_val = min(self.sentiment_history)
        max_val = max(self.sentiment_history)

        if max_val == min_val:
            # All values are the same
            chart_data = ["▄"] * len(self.sentiment_history[-30:])
        else:
            for val in self.sentiment_history[-30:]:
                normalized = (val - min_val) / (max_val - min_val)
                char_index = min(7, max(0, int(normalized * 7)))
                chart_data.append(chart_chars[char_index])

        # Create chart display
        chart_line = "".join(chart_data)
        current_val = self.sentiment_history[-1]

        # Determine trend
        if len(self.sentiment_history) >= 2:
            trend = "📈" if self.sentiment_history[-1] > self.sentiment_history[-2] else "📉"
        else:
            trend = "➡️"

        chart_text = f"Trend: {chart_line}\n"
        chart_text += f"Current: {current_val:+.3f} {trend}\n"
        chart_text += f"Range: {min_val:.3f} to {max_val:.3f}"

        self.update(Panel(chart_text, title="📈 Live Sentiment"))


class NewsTreeView(Tree):
    """Interactive tree view for news articles organized by sentiment"""

    def __init__(self, **kwargs):
        super().__init__("📰 Recent News", **kwargs)
        self.show_root = False

    def update_news(self, news_data: List[Dict], sentiment_scores: List[float], sentiment_details: List[Dict]):
        """Update tree with news data organized by sentiment"""
        self.clear()

        # Create sentiment category nodes
        positive_node = self.root.add("🟢 Positive News", expand=True)
        neutral_node = self.root.add("🟡 Neutral News", expand=True)
        negative_node = self.root.add("🔴 Negative News", expand=True)

        # Sort articles by sentiment for better organization
        combined_data = list(zip(news_data[:20], sentiment_scores[:20], sentiment_details[:20]))
        combined_data.sort(key=lambda x: x[1], reverse=True)  # Sort by sentiment score

        for article, sentiment, detail in combined_data:
            headline = article['headline']
            if len(headline) > 60:
                headline = headline[:57] + "..."

            ticker = article.get('ticker', 'N/A')
            time_ago = article.get('time_ago', 'Unknown')

            # Create node text with metadata
            node_text = f"[{time_ago}] {ticker}: {headline}"

            # Add to appropriate category
            if sentiment > 0.1:
                leaf = positive_node.add_leaf(node_text)
            elif sentiment < -0.1:
                leaf = negative_node.add_leaf(node_text)
            else:
                leaf = neutral_node.add_leaf(node_text)

            # Store article data for potential modal display
            leaf.data = {
                'article': article,
                'sentiment': sentiment,
                'detail': detail
            }

    def on_tree_node_selected(self, event: Tree.NodeSelected) -> None:
        """Handle node selection - show article details"""
        if hasattr(event.node, 'data') and event.node.data:
            article_data = event.node.data
            article_info = {
                'headline': article_data['article']['headline'],
                'time_ago': article_data['article'].get('time_ago', 'Unknown'),
                'sentiment': article_data['sentiment'],
                'category': article_data['detail'].get('category', 'N/A'),
                'url': article_data['article'].get('url', ''),
                'mentioned_tickers': article_data['detail'].get('mentioned_tickers', [])
            }
            self.app.push_screen(ArticleDetailModal(article_info))


class SummaryPanel(Static):
    """Enhanced panel showing market summary (policy moved to dedicated tab)"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.border_title = "📊 Market Overview"

    def update_data(self, sentiment_analysis, policy_analysis, market_health, market_data=None):
        """Update the summary panel with market data (policy analysis in separate tab)"""
        # Market sentiment
        market_mood = sentiment_analysis.get('market_mood', 'N/A')
        market_score = sentiment_analysis.get('average_sentiment', 0)
        market_emoji = self._get_mood_emoji(market_score, market_mood)

        # Recommendation
        recommendation = market_health.get('recommendation', 'N/A') if market_health else 'N/A'
        market_trend = market_health.get('market_trend', 'Unknown') if market_health else 'Unknown'

        # Create enhanced summary with clear section headers
        table = Table.grid(padding=1)
        table.add_column("Section", style="bold cyan", width=22)
        table.add_column("Details", width=50)

        # MARKET SENTIMENT SECTION
        pos_pct = sentiment_analysis.get('positive_percentage', 0)
        neg_pct = sentiment_analysis.get('negative_percentage', 0)
        total_articles = sentiment_analysis.get('total_articles', 0)

        table.add_row("📊 MARKET SENTIMENT", "")
        table.add_row("", f"{market_emoji} {market_mood} ({market_score:+.3f})")
        table.add_row("", f"📈 {pos_pct:.0f}% Positive | 📉 {neg_pct:.0f}% Negative")
        table.add_row("", f"📊 {total_articles} Articles Analyzed")
        table.add_row("", "")

        # MARKET INDICES SECTION
        if market_data:
            table.add_row("📈 MARKET INDICES", "")
            for ticker, data in list(market_data.items())[:4]:  # Show top 4 indices
                change = data.get('price_change', 0)
                emoji = "📈" if change > 0 else "📉" if change < 0 else "➡️"
                name = data.get('name', ticker)
                table.add_row("", f"{emoji} {name}: {change:+.1f}%")
            table.add_row("", "")

        # TRADING RECOMMENDATION SECTION
        table.add_row("🚀 RECOMMENDATION", "")
        table.add_row("", f"Action: {recommendation}")
        table.add_row("", f"Trend: {market_trend}")
        table.add_row("", "")

        # POLICY REFERENCE
        if policy_analysis:
            policy_mood = policy_analysis.get('policy_mood', 'N/A')
            policy_score = policy_analysis.get('policy_sentiment', 0)
            policy_emoji = self._get_mood_emoji(policy_score, policy_mood)
            table.add_row("🏛️ POLICY SUMMARY", "")
            table.add_row("", f"{policy_emoji} {policy_mood} ({policy_score:+.3f})")
            table.add_row("", "📋 See Policy tab for detailed analysis")

        # Update the widget content
        self.update(table)
    
    def _get_mood_emoji(self, sentiment_score, mood_text):
        """Get appropriate emoji based on sentiment"""
        if sentiment_score > 0.05:
            return "😊"
        elif sentiment_score < -0.05:
            return "😠"
        else:
            return "😐"


class NewsPanel(ScrollableContainer):
    """Panel showing recent news with multi-ticker information"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.border_title = "📰 Recent Market News"
    
    def update_data(self, news_data, sentiment_scores, sentiment_details, multi_ticker_articles):
        """Update the news panel with new data"""
        # Clear existing content
        self.remove_children()
        
        # Create a mapping of articles to their multi-ticker data
        multi_ticker_map = {}
        for mt_article in multi_ticker_articles:
            article_index = mt_article['article_index']
            multi_ticker_map[article_index] = mt_article
        
        # Combine and sort by recency
        combined_data = []
        for i, article in enumerate(news_data[:30]):
            if i < len(sentiment_scores):
                combined_data.append({
                    'article': article,
                    'sentiment_score': sentiment_scores[i],
                    'article_index': i,
                    'sentiment_detail': sentiment_details[i] if i < len(sentiment_details) else {}
                })
        
        combined_data.sort(key=lambda x: x['article'].get('datetime', ''), reverse=True)
        
        # Add news items
        for i, item in enumerate(combined_data[:20], 1):
            article = item['article']
            sentiment_score = item['sentiment_score']
            article_index = item['article_index']
            sentiment_detail = item['sentiment_detail']
            
            # Create news item widget
            news_item = self._create_news_item(
                i, article, sentiment_score, article_index, 
                multi_ticker_map, sentiment_detail
            )
            self.mount(news_item)
    
    def _create_news_item(self, index, article, sentiment_score, article_index, multi_ticker_map, sentiment_detail):
        """Create a single news item widget"""
        # Sentiment emoji
        if sentiment_score > 0.1:
            emoji = "🟢"
        elif sentiment_score > -0.1:
            emoji = "🟡"
        else:
            emoji = "🔴"
        
        # Get ticker information
        primary_ticker = article.get('ticker', 'N/A')
        time_info = article.get('time_ago', 'Unknown')
        headline = article['headline']
        
        # Check for multi-ticker information
        mentioned_tickers = []
        ticker_sentiments = {}
        
        if article_index in multi_ticker_map:
            mt_data = multi_ticker_map[article_index]
            mentioned_tickers = mt_data['mentioned_tickers']
            ticker_sentiments = mt_data['ticker_sentiments']
        elif 'mentioned_tickers' in sentiment_detail:
            mentioned_tickers = sentiment_detail['mentioned_tickers']
            ticker_sentiments = sentiment_detail.get('ticker_sentiments', {})
        
        # Create content - escape markup characters
        content_lines = []
        # Escape square brackets to prevent markup interpretation
        safe_time_info = time_info.replace("[", "\\[").replace("]", "\\]")
        safe_headline = headline.replace("[", "\\[").replace("]", "\\]")

        content_lines.append(f"{index:2d}. {emoji} \\[{safe_time_info}\\]")
        content_lines.append(f"    {safe_headline}")

        # Show tickers
        if len(mentioned_tickers) > 1:
            # Multi-ticker article
            ticker_parts = []
            for ticker in mentioned_tickers[:4]:
                if ticker in ticker_sentiments:
                    ticker_sentiment = ticker_sentiments[ticker]
                    if ticker_sentiment['sentiment_category'] == 'Positive':
                        ticker_emoji = "🟢"
                    elif ticker_sentiment['sentiment_category'] == 'Negative':
                        ticker_emoji = "🔴"
                    else:
                        ticker_emoji = "🟡"
                    ticker_parts.append(f"{ticker_emoji}{ticker}")
                else:
                    ticker_parts.append(f"⚪{ticker}")

            if len(mentioned_tickers) > 4:
                ticker_parts.append(f"+{len(mentioned_tickers)-4}")

            content_lines.append(f"    🔗 {' '.join(ticker_parts)}")
        else:
            content_lines.append(f"    📊 {primary_ticker}")

        return Static("\n".join(content_lines), classes="news-item", markup=False)


class TickersPanel(Static):
    """Enhanced panel showing top performing tickers with detailed metrics"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.border_title = "🏆 Top Sentiment Performers"

    def update_data(self, sector_rankings, ticker_rankings, price_changes, current_prices):
        """Update the tickers panel with new data"""
        table = Table()
        table.add_column("Rank", style="bold cyan", width=4)
        table.add_column("Ticker", style="bold", width=8)
        table.add_column("Price & Change", width=18)
        table.add_column("Sentiment", width=10)

        # Add header row
        table.title = "Top 6 Tickers by Sentiment Score"

        # Add top tickers
        for i, ticker in enumerate(ticker_rankings[:6], 1):
            ticker_symbol = ticker['ticker']
            price_change = price_changes.get(ticker_symbol, 0.0)
            current_price = current_prices.get(ticker_symbol) if current_prices else None

            # Price display with emoji
            price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"
            price_str = f"${current_price:.2f}" if current_price else "N/A"
            price_display = f"{price_emoji} {price_str} ({price_change:+.1f}%)"

            # Sentiment score with color
            sentiment_score = ticker['overall_score']
            if sentiment_score > 0.3:
                sentiment_style = "green"
            elif sentiment_score > 0.1:
                sentiment_style = "yellow"
            else:
                sentiment_style = "white"

            table.add_row(
                f"{i}",
                ticker_symbol,
                price_display,
                Text(f"{sentiment_score:.3f}", style=sentiment_style)
            )

        self.update(table)


class SectorsPanel(Static):
    """Enhanced panel showing top performing sectors with detailed metrics"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.border_title = "🏭 Sector Performance Rankings"

    def update_data(self, sector_rankings, price_changes):
        """Update the sectors panel with new data"""
        if not sector_rankings:
            self.update("No sector data available")
            return

        table = Table()
        table.add_column("Rank", style="bold cyan", width=4)
        table.add_column("Sector", width=14)
        table.add_column("Strength", width=8)
        table.add_column("Top Ticker", width=12)

        table.title = "Top 5 Sectors by Sentiment Strength"

        # Add top sectors
        for i, sector in enumerate(sector_rankings[:5], 1):
            # Sector sentiment emoji
            avg_sentiment = sector['average_sentiment']
            if avg_sentiment > 0.1:
                emoji = "🟢"
                sentiment_style = "green"
            elif avg_sentiment > 0:
                emoji = "🟡"
                sentiment_style = "yellow"
            else:
                emoji = "🔴"
                sentiment_style = "red"

            # Top ticker info
            top_ticker = sector['top_ticker']
            price_change = price_changes.get(top_ticker, 0.0)
            price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"

            table.add_row(
                f"{i}",
                f"{emoji} {sector['sector'][:12]}",
                Text(f"{sector['sector_strength']:.2f}", style=sentiment_style),
                f"{price_emoji} {top_ticker}"
            )

        self.update(table)


class MultiTickerPanel(Static):
    """Enhanced panel showing multi-ticker analysis with clear metrics"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.border_title = "🔄 Cross-Ticker Analysis"

    def update_data(self, multi_ticker_articles, cross_ticker_analysis):
        """Update the multi-ticker panel with new data"""
        if not multi_ticker_articles:
            self.update("No multi-ticker articles found")
            return

        # Create structured table
        table = Table.grid(padding=1)
        table.add_column("Metric", style="bold cyan", width=18)
        table.add_column("Value", width=30)

        # Summary metrics
        conflicts_count = len(cross_ticker_analysis.get('sentiment_conflicts', []))
        pairs_count = len(cross_ticker_analysis.get('ticker_pairs', {}))

        table.add_row("📊 ANALYSIS SUMMARY", "")
        table.add_row("", f"Multi-ticker Articles: {len(multi_ticker_articles)}")
        table.add_row("", f"Sentiment Conflicts: {conflicts_count}")
        table.add_row("", f"Ticker Pairs Found: {pairs_count}")
        table.add_row("", "")

        # Show top conflicts
        if cross_ticker_analysis.get('sentiment_conflicts'):
            table.add_row("⚠️ TOP CONFLICTS", "")
            for i, conflict in enumerate(cross_ticker_analysis['sentiment_conflicts'][:3], 1):
                pos_tickers = ", ".join(conflict['positive_tickers'][:2])
                neg_tickers = ", ".join(conflict['negative_tickers'][:2])
                table.add_row("", f"{i}. 🟢 {pos_tickers} vs 🔴 {neg_tickers}")

        self.update(table)


class PolicySummaryPanel(Static):
    """Comprehensive policy analysis summary panel"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.border_title = "🏛️ Government Policy Analysis"

    def update_data(self, policy_analysis):
        """Update the policy summary panel with comprehensive data"""
        if not policy_analysis:
            self.update("No policy data available")
            return

        # Create comprehensive policy summary
        table = Table.grid(padding=1)
        table.add_column("Section", style="bold cyan", width=25)
        table.add_column("Details", width=60)

        # Policy sentiment overview
        policy_mood = policy_analysis.get('policy_mood', 'No Data')
        policy_sentiment = policy_analysis.get('policy_sentiment', 0)
        policy_emoji = self._get_policy_emoji(policy_sentiment, policy_mood)

        table.add_row("📊 POLICY SENTIMENT", "")
        table.add_row("", f"{policy_emoji} {policy_mood} ({policy_sentiment:+.3f})")
        table.add_row("", "")

        # Article statistics
        total_articles = policy_analysis.get('total_policy_articles', 0)
        high_impact_count = len(policy_analysis.get('high_impact_articles', []))

        table.add_row("📄 ARTICLE ANALYSIS", "")
        table.add_row("", f"Total Policy Articles: {total_articles}")
        table.add_row("", f"High Impact Articles: {high_impact_count}")
        table.add_row("", f"Impact Rate: {(high_impact_count/total_articles*100) if total_articles > 0 else 0:.1f}%")
        table.add_row("", "")

        # Policy categories if available
        if 'policy_categories' in policy_analysis:
            table.add_row("🏷️ POLICY CATEGORIES", "")
            categories = policy_analysis['policy_categories']
            for category, count in list(categories.items())[:5]:
                table.add_row("", f"{category}: {count} articles")
            table.add_row("", "")

        # Market impact assessment
        market_impact = policy_analysis.get('market_impact_score', 0)
        if market_impact != 0:
            impact_emoji = "📈" if market_impact > 0 else "📉" if market_impact < 0 else "➡️"
            table.add_row("💼 MARKET IMPACT", "")
            table.add_row("", f"{impact_emoji} Impact Score: {market_impact:+.3f}")
            table.add_row("", f"Assessment: {self._get_impact_assessment(market_impact)}")

        self.update(table)

    def _get_policy_emoji(self, sentiment_score, mood_text):
        """Get appropriate emoji for policy sentiment"""
        if sentiment_score > 0.1:
            return "🟢"
        elif sentiment_score > 0:
            return "🟡"
        elif sentiment_score < -0.1:
            return "🔴"
        else:
            return "⚪"

    def _get_impact_assessment(self, impact_score):
        """Get market impact assessment text"""
        if impact_score > 0.2:
            return "Strongly Positive"
        elif impact_score > 0.05:
            return "Moderately Positive"
        elif impact_score > -0.05:
            return "Neutral"
        elif impact_score > -0.2:
            return "Moderately Negative"
        else:
            return "Strongly Negative"


class PolicyArticlesPanel(ScrollableContainer):
    """Panel showing high-impact policy articles"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.border_title = "⚡ High Impact Policy Articles"

    def update_data(self, policy_analysis):
        """Update with high-impact policy articles"""
        # Clear existing content
        self.remove_children()

        if not policy_analysis or not policy_analysis.get('high_impact_articles'):
            self.mount(Static("No high-impact policy articles found"))
            return

        high_impact_articles = policy_analysis['high_impact_articles'][:15]  # Show top 15

        for i, article in enumerate(high_impact_articles, 1):
            # Create article item
            headline = article.get('headline', 'No headline')
            if len(headline) > 80:
                headline = headline[:77] + "..."

            time_ago = article.get('time_ago', 'Unknown time')
            impact_score = article.get('impact_score', 0)
            category = article.get('category', 'General')

            # Impact level emoji
            if impact_score > 0.7:
                impact_emoji = "🔥"
            elif impact_score > 0.5:
                impact_emoji = "⚡"
            elif impact_score > 0.3:
                impact_emoji = "📢"
            else:
                impact_emoji = "📄"

            # Create article content with hyperlink if URL available
            content_lines = []
            content_lines.append(f"{i:2d}. {impact_emoji} [{time_ago}] {category}")

            # Add hyperlink if URL is available
            url = article.get('url', '')
            if url:
                # Create OSC 8 hyperlink
                hyperlink = f"\033]8;;{url}\033\\{headline}\033]8;;\033\\"
                content_lines.append(f"    {hyperlink}")
            else:
                content_lines.append(f"    {headline}")

            content_lines.append(f"    Impact: {impact_score:.2f} | Sentiment: {article.get('sentiment', 0):+.2f}")

            article_widget = Static("\n".join(content_lines), classes="news-item")
            self.mount(article_widget)


class PolicyTimelinePanel(Static):
    """Panel showing policy timeline and trends"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.border_title = "📈 Policy Sentiment Timeline"

    def update_data(self, policy_analysis):
        """Update with policy timeline data"""
        if not policy_analysis:
            self.update("No policy timeline data available")
            return

        # Create timeline visualization
        content_lines = []
        content_lines.append("📊 POLICY SENTIMENT TRENDS")
        content_lines.append("")

        # Current sentiment
        current_sentiment = policy_analysis.get('policy_sentiment', 0)
        trend_emoji = "📈" if current_sentiment > 0 else "📉" if current_sentiment < 0 else "➡️"
        content_lines.append(f"Current Trend: {trend_emoji} {current_sentiment:+.3f}")
        content_lines.append("")

        # Recent policy changes if available
        if 'recent_changes' in policy_analysis:
            content_lines.append("🔄 RECENT POLICY CHANGES:")
            for change in policy_analysis['recent_changes'][:5]:
                change_emoji = "🟢" if change.get('impact', 0) > 0 else "🔴" if change.get('impact', 0) < 0 else "🟡"
                content_lines.append(f"  {change_emoji} {change.get('description', 'Policy change')}")
            content_lines.append("")

        # Policy sectors affected
        if 'affected_sectors' in policy_analysis:
            content_lines.append("🏭 AFFECTED SECTORS:")
            sectors = policy_analysis['affected_sectors']
            for sector, impact in list(sectors.items())[:5]:
                sector_emoji = "📈" if impact > 0 else "📉" if impact < 0 else "➡️"
                content_lines.append(f"  {sector_emoji} {sector}: {impact:+.2f}")

        self.update("\n".join(content_lines))


class EnhancedFinancialDashboard(App):
    """Enhanced Interactive Financial Dashboard with Tabbed Interface"""

    CSS = """
    #ticker-modal, #article-modal {
        align: center middle;
        width: 80;
        height: 20;
        background: $surface;
        border: thick $primary;
    }

    #modal-title {
        dock: top;
        height: 3;
        content-align: center middle;
        text-style: bold;
        background: $primary;
        color: $text;
    }

    #article-content {
        padding: 1;
        height: 1fr;
    }

    .data-table {
        height: 1fr;
    }

    .chart-container {
        height: 8;
        border: solid $primary;
        margin: 1;
    }

    .controls-panel {
        height: 6;
        border: solid $secondary;
        margin: 1;
    }

    .status-bar {
        dock: bottom;
        height: 1;
        background: $primary;
        color: $text;
    }

    TabbedContent {
        height: 1fr;
    }

    TabPane {
        padding: 1;
    }

    .news-item {
        margin: 1;
        padding: 1;
    }

    #left-panel {
        width: 1fr;
        margin: 1;
    }

    #right-panel {
        width: 2fr;
        margin: 1;
    }

    #summary-panel {
        height: 16;
        margin: 1;
        border: solid $primary;
    }

    #tickers-panel {
        height: 1fr;
        margin: 1;
        border: solid $secondary;
        width: 1fr;
    }

    #sectors-panel {
        height: 1fr;
        margin: 1;
        border: solid $secondary;
        width: 1fr;
    }

    #multi-ticker-panel {
        height: 1fr;
        margin: 1;
        border: solid $secondary;
        width: 1fr;
    }
    """

    BINDINGS = [
        Binding("q", "quit", "Quit"),
        Binding("r", "refresh", "Refresh"),
        Binding("f", "toggle_filter", "Toggle Filters"),
        Binding("1", "switch_tab('overview')", "Overview"),
        Binding("2", "switch_tab('tickers')", "Tickers"),
        Binding("3", "switch_tab('news')", "News"),
        Binding("4", "switch_tab('policy')", "Policy"),
        Binding("ctrl+e", "export_data", "Export"),
    ]

    TITLE = "🚀 Enhanced Financial Sentiment Analyzer"
    SUB_TITLE = "Interactive Real-time Market Analysis"

    # Reactive variables for data
    current_sentiment = var(0.0)
    last_update = var("")
    auto_refresh_enabled = var(True)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.data_cache = {}
        self.quick_mode = False
        self.verbose_mode = False

    def compose(self) -> ComposeResult:
        """Create the enhanced dashboard layout"""
        yield Header()

        with TabbedContent(initial="overview"):
            # Overview Tab - Enhanced summary dashboard
            with TabPane("📊 Overview", id="overview"):
                with Vertical():
                    # Top row - Market summary
                    yield SummaryPanel(id="summary-panel")

                    # Bottom row - Performance metrics in columns
                    with Horizontal():
                        yield TickersPanel(id="tickers-panel")
                        yield SectorsPanel(id="sectors-panel")
                        yield MultiTickerPanel(id="multi-ticker-panel")

            # Interactive Tickers Tab
            with TabPane("🏆 Tickers", id="tickers"):
                with Vertical():
                    yield FilterControls(classes="controls-panel")
                    yield InteractiveTickerTable(classes="data-table")

            # News Tree Tab
            with TabPane("📰 News", id="news"):
                with Horizontal():
                    with Vertical():
                        yield NewsTreeView(id="news-tree")
                    with Vertical():
                        yield RealTimeChart(classes="chart-container")
                        yield Static("Select an article from the tree to view details", id="news-details")

            # Policy Analysis Tab
            with TabPane("🏛️ Policy", id="policy"):
                with Vertical():
                    # Top row - Policy summary
                    yield PolicySummaryPanel(id="policy-summary-panel")

                    # Bottom row - Policy details in columns
                    with Horizontal():
                        yield PolicyArticlesPanel(id="policy-articles-panel")
                        yield PolicyTimelinePanel(id="policy-timeline-panel")

        yield Static("🔄 Auto-refresh: ON | Last update: Never",
                    classes="status-bar", id="status-bar")
        yield Footer()

    def on_mount(self) -> None:
        """Initialize the dashboard with data"""
        self.set_interval(60, self.update_dashboard_data)  # Auto-refresh every 60 seconds
        self.call_later(self.update_dashboard_data)  # Initial data load

    async def update_dashboard_data(self) -> None:
        """Update all dashboard data"""
        try:
            self.update_status("🔄 Refreshing data...")

            # Import here to avoid circular imports
            from financial_analyzer import analyze_all_data, fetch_all_data

            # Fetch new data using the configured quick mode
            quick_mode = getattr(self, 'quick_mode', False)
            news_data, _, government_data, _, market_data = fetch_all_data(quick_mode=quick_mode)

            # Analyze data
            (sentiment_analysis, policy_analysis, market_health, sector_rankings,
             ticker_rankings, price_changes, current_prices, sentiment_scores,
             sentiment_details, multi_ticker_articles, cross_ticker_analysis) = analyze_all_data(news_data, government_data, market_data)

            # Store data for other tabs
            self.data_cache = {
                'sentiment_analysis': sentiment_analysis,
                'policy_analysis': policy_analysis,
                'market_health': market_health,
                'sector_rankings': sector_rankings,
                'ticker_rankings': ticker_rankings,
                'price_changes': price_changes,
                'current_prices': current_prices,
                'sentiment_scores': sentiment_scores,
                'sentiment_details': sentiment_details,
                'multi_ticker_articles': multi_ticker_articles,
                'cross_ticker_analysis': cross_ticker_analysis,
                'news_data': news_data
            }

            # Update Overview tab panels
            try:
                summary_panel = self.query_one("#summary-panel", SummaryPanel)
                summary_panel.update_data(sentiment_analysis, policy_analysis, market_health, market_data)
            except:
                pass

            try:
                tickers_panel = self.query_one("#tickers-panel", TickersPanel)
                tickers_panel.update_data(sector_rankings, ticker_rankings, price_changes, current_prices)
            except:
                pass

            try:
                sectors_panel = self.query_one("#sectors-panel", SectorsPanel)
                sectors_panel.update_data(sector_rankings, price_changes)
            except:
                pass

            try:
                multi_ticker_panel = self.query_one("#multi-ticker-panel", MultiTickerPanel)
                multi_ticker_panel.update_data(multi_ticker_articles, cross_ticker_analysis)
            except:
                pass

            # Update Interactive Tickers tab
            try:
                ticker_table = self.query_one(InteractiveTickerTable)
                ticker_table.update_data(ticker_rankings, price_changes, current_prices)
            except:
                pass

            # Update News Tree tab
            try:
                news_tree = self.query_one("#news-tree", NewsTreeView)
                news_tree.update_news(news_data, sentiment_scores, sentiment_details)

                chart = self.query_one(RealTimeChart)
                if sentiment_analysis:
                    chart.update_sentiment(sentiment_analysis.get('average_sentiment', 0))
            except:
                pass

            # Update Policy tab
            try:
                policy_summary_panel = self.query_one("#policy-summary-panel", PolicySummaryPanel)
                policy_summary_panel.update_data(policy_analysis)
            except:
                pass

            try:
                policy_articles_panel = self.query_one("#policy-articles-panel", PolicyArticlesPanel)
                policy_articles_panel.update_data(policy_analysis)
            except:
                pass

            try:
                policy_timeline_panel = self.query_one("#policy-timeline-panel", PolicyTimelinePanel)
                policy_timeline_panel.update_data(policy_analysis)
            except:
                pass

            # Update reactive variables
            self.current_sentiment = sentiment_analysis.get('average_sentiment', 0)
            self.last_update = datetime.now().strftime("%H:%M:%S")

            self.update_status(f"✅ Updated at {self.last_update}")

        except Exception as e:
            # Handle errors gracefully
            self.notify(f"Error updating data: {str(e)}", severity="error")
            self.update_status(f"❌ Error: {str(e)}")



    def update_status(self, message: str) -> None:
        """Update status bar"""
        try:
            status_bar = self.query_one("#status-bar", Static)
            refresh_status = "ON" if self.auto_refresh_enabled else "OFF"
            status_bar.update(f"🔄 Auto-refresh: {refresh_status} | {message}")
        except:
            pass

    def action_refresh(self) -> None:
        """Manual refresh action"""
        self.call_later(self.update_dashboard_data)

    def action_toggle_filter(self) -> None:
        """Toggle filter visibility"""
        try:
            filters = self.query_one(".controls-panel")
            filters.display = not filters.display
        except:
            pass

    def action_switch_tab(self, tab_id: str) -> None:
        """Switch to specific tab (overview, tickers, news, policy)"""
        try:
            tabbed_content = self.query_one(TabbedContent)
            tabbed_content.active = tab_id
        except:
            pass

    def action_export_data(self) -> None:
        """Export current data"""
        self.notify("Export functionality would be implemented here", severity="information")


def run_textual_dashboard():
    """Run the Enhanced Textual dashboard"""
    app = EnhancedFinancialDashboard()
    app.run()


def run_enhanced_textual_dashboard(quick_mode=False, verbose=False):
    """Run the Enhanced Textual dashboard with configuration options"""
    app = EnhancedFinancialDashboard()

    # Store configuration in the app for use by data fetching
    app.quick_mode = quick_mode
    app.verbose_mode = verbose

    if verbose:
        # Show a brief startup message before launching the dashboard
        print("🚀 Launching Enhanced Financial Sentiment Analyzer Dashboard...")
        print("⚡ Quick mode:", "ON" if quick_mode else "OFF")
        print("📊 Loading interface...\n")

    app.run()


if __name__ == "__main__":
    run_textual_dashboard()
